; ===================================================================
; CAD TO EXCEL - PHIEN BAN 3.0 TICH HOP
; Tac gia: <PERSON>it <PERSON>
; Ngay phat hanh: 20/12/2024
; Phien ban: 3.0 Integrated
; ===================================================================
;
; HUONG DAN SU DUNG:
; 1. Load file: APPLOAD -> chon file .lsp
; 2. Su dung cac lenh E0, E1, E11-E15, E2, E21-E25, E3, E31-E35, E4, E41-E44
; 3. Cau hinh: Dung lenh E0 de thiet lap toan cuc
; 4. Excel phai duoc mo truoc khi chay lenh
;
; DANH SACH LENH:
; === NHOM E1: XUAT CELL ===
; E1  - Xuat text/dim vao cell (co he so factor)
; E11 - Xuat text/dim voi he so nhan them (=(n1+n2+...)*a)
; E12 - Xuat text/dim vao cell (text giu nguyen + dim co factor)
; E14 - Dat nhanh he so (factor)=1
; E15 - Dat nhanh he so (factor)=0.001
;
; === NHOM E2: XUAT COL/ROW/ARRAY ===
; E2  - Xuat theo 3 che do (Col/Row/Array) voi thu tu selection
; E21 - Xuat nhanh ra cot (vi tri chuot tu dong nhay xuong o cuoi cung)
; E22 - Xuat nhanh ra hang (vi tri chuot tu dong nhay qua phai o cuoi cung)
; E23 - Xuat nhanh ra mang (vi tri chuot tu dong nhay xuong o cuoi cung)
; E24 - Bat nhanh Number (Y)
; E25 - Tat nhanh Number (N)
;
; === NHOM E3: GHI HANDLE ===
; E3  - Chi ghi handle vao comment
; E31 - Mo file Cad theo comment (Handle To CAD)
; E32 - Zoom va highlight doi tuong theo handle (Zoom To Handle)
; E33 - Zoom va select doi tuong theo handle (Select To Handle)
; E34 - Bat nhanh handle (gan che do thiet lap handle Y)
; E35 - Tat nhanh handle (gan che do thiet lap handle N)
;
; === NHOM E4: XUAT TABLE ===
; E4  - Xuat bang theo line
; E41 - Xuat bang theo line (giu format bang)
; E42 - Xuat bang theo Table
; E43 - Xuat bang tu excel qua cad
; E44 - Cong tac mo Frame (Y/N)
;
; === THIET LAP ===
; E0  - Mo bang thiet lap toan cuc (Handle/Frame/Symbol/Factor/Jump/Tolerance/Number)
;
; ===================================================================

(setvar "MODEMACRO" "**Zit Dai Ka**")

; Khoi tao cac bien toan cuc
(if (not *E6-handle*) (setq *E6-handle* "N"))
(if (not *E6-frame*) (setq *E6-frame* "N"))
(if (not *E6-symbol*) (setq *E6-symbol* "+"))
(if (not *E6-factor*) (setq *E6-factor* "1"))
(if (not *E6-jump*) (setq *E6-jump* "3"))
(if (not *E6-tolerance*) (setq *E6-tolerance* "0.1"))
(if (not *E6-number*) (setq *E6-number* "N"))

; Ham load Express Tools manh me cho AutoCAD 2025+
(defun c:load_acetutilARX (/ fn f n path)
  (if (not (member "acetutil.arx" (arx)))
    (progn
		(setq fn (findfile "acetutil.arx"))
		(if fn
			(progn
				(arxload fn)
				(princ "\nLoaded acetutil.arx successfully.")
				; Load cac file .fas lien quan
				(setq path (vl-filename-directory fn))
				(foreach fasfile '("acetutil.fas" "acetutil2.fas" "acetutil3.fas" "acetutil4.fas")
					(setq f (strcat path "\\" fasfile))
					(if (findfile f)
						(progn
							(load f)
							(princ (strcat "\nLoaded " fasfile))
						)
					)
				)
			)
			(progn
				(princ "\nCannot find acetutil.arx")
				; Thu tim trong thu muc Express
				(setq path (strcat (getvar "ACADLOCATION") "Express"))
				(setq fn (strcat path "\\acetutil.arx"))
				(if (findfile fn)
					(progn
						(arxload fn)
						(princ "\nLoaded acetutil.arx from Express folder.")
					)
					(princ "\nFailed to load acetutil.arx")
				)
			)
		)
	)
	(princ "\nacetutil.arx already loaded.")
  )
  ; Load Express Tools neu chua co
  (if (not acet-util-ver)
	(progn
		(acet-load-expresstools)
		(princ "\nExpress Tools loaded.")
	)
	(princ "\nExpress Tools already loaded.")
  )
  (princ)
)

; ===================================================================
; CAC HAM HO TRO CHUNG
; ===================================================================

; Ham ket noi Excel
(defun CONNECT-EXCEL ( / xlapp xlcells startrow startcol activecell result)
	(setq result nil)
	(vl-catch-all-apply
		'(lambda ()
			(setq xlapp (vlax-get-or-create-object "Excel.Application"))
			(if xlapp
				(progn
					(setq xlcells (vlax-get-property xlapp "ActiveSheet"))
					(setq activecell (vlax-get-property xlapp "ActiveCell"))
					(setq startrow (vlax-get-property activecell "Row"))
					(setq startcol (vlax-get-property activecell "Column"))
					(setq result (list xlapp xlcells startrow startcol))
				)
				(progn
					(princ "\nKhong the ket noi Excel!")
					(setq result nil)
				)
			)
		)
	)
	(if (not result)
		(princ "\nLoi: Khong the ket noi Excel hoac Excel chua mo!")
	)
	result
)

; Ham di chuyen cursor
(defun MOVE-CURSOR ( xlcells row col / targetcell)
	(vl-catch-all-apply
		'(lambda ()
			(setq targetcell (vlax-get-property xlcells "Cells" row col))
			(vlax-invoke-method targetcell "Select")
		)
	)
)

; Ham lay handle cua cac doi tuong
(defun GET-HANDLES ( ss / handlelist i ent)
	(setq handlelist '())
	(if ss
		(progn
			(setq i 0)
			(repeat (sslength ss)
				(setq ent (ssname ss i))
				(setq handlelist (cons (vla-get-handle (vlax-ename->vla-object ent)) handlelist))
				(setq i (1+ i))
			)
		)
	)
	(reverse handlelist)
)

; Ham tao comment text
(defun CREATE-COMMENT-TEXT ( handlelist dwgpath dwgname / commenttext)
	(setq commenttext "")
	(if handlelist
		(progn
			(foreach handle handlelist
				(setq commenttext (strcat commenttext handle ";"))
			)
			(setq commenttext (strcat commenttext "\nFileCad: " dwgpath))
		)
	)
	commenttext
)

; Ham ghi comment vao cell
(defun WRITE-COMMENT-TO-CELL ( targetcell commenttext / comment)
	(vl-catch-all-apply '(lambda ()
		; Xoa comment cu neu co
		(setq comment (vlax-get-property targetcell "Comment"))
		(if comment (vlax-invoke-method comment "Delete"))
		
		; Tao comment moi
		(vlax-invoke-method targetcell "AddComment" commenttext)
		(setq comment (vlax-get-property targetcell "Comment"))
		
		; Dinh dang comment (khong bold)
		(if comment
			(progn
				(setq commentshape (vlax-get-property comment "Shape"))
				(setq textframe (vlax-get-property commentshape "TextFrame"))
				(setq characters (vlax-get-property textframe "Characters"))
				(setq font (vlax-get-property characters "Font"))
				(vlax-put-property font "Bold" :vlax-false)
			)
		)
	))
)

; Ham xu ly MTEXT format codes
(defun CLEAN-MTEXT ( text / result)
	(setq result text)
	; Loai bo cac ma format \xxx;
	(while (vl-string-search "\\" result)
		(setq pos (vl-string-search "\\" result))
		(setq end-pos (vl-string-search ";" result pos))
		(if end-pos
			(setq result (strcat (substr result 1 (1- pos)) (substr result (+ end-pos 2))))
			(setq result (substr result 1 (1- pos)))
		)
	)
	; Loai bo { va }
	(setq result (vl-string-subst "" "{" result))
	(setq result (vl-string-subst "" "}" result))
	result
)

; Ham chuyen doi ky tu dac biet
(defun CONVERT-SPECIAL-CHARS ( text / result)
	(setq result text)
	(setq result (vl-string-subst "Ø" "%%c" result))
	(setq result (vl-string-subst "±" "%%p" result))
	result
)

; Ham lay noi dung text/mtext/dimension
(defun GET-TEXT-CONTENT ( ent / obj content)
	(setq content "")
	(vl-catch-all-apply
		'(lambda ()
			(setq obj (vlax-ename->vla-object ent))
			(cond
				((= (vla-get-objectname obj) "AcDbText")
					(setq content (vla-get-textstring obj))
				)
				((= (vla-get-objectname obj) "AcDbMText")
					(setq content (vla-get-textstring obj))
					(setq content (CLEAN-MTEXT content))
				)
				((wcmatch (vla-get-objectname obj) "*Dimension*")
					(setq content (rtos (vla-get-measurement obj) 2 0))
				)
				(t (setq content ""))
			)
		)
	)
	(if (not content) (setq content ""))
	(CONVERT-SPECIAL-CHARS content)
)

; Ham lay gia tri so tu text
(defun EXTRACT-NUMBER ( text / result pos)
	(setq result "")
	(setq pos 0)
	(while (< pos (strlen text))
		(setq char (substr text (1+ pos) 1))
		(if (or (and (>= (ascii char) 48) (<= (ascii char) 57)) ; so 0-9
				(= char ".") (= char ","))
			(setq result (strcat result char))
		)
		(setq pos (1+ pos))
	)
	; Chuyen dau phay thanh dau cham
	(setq result (vl-string-subst "." "," result))
	result
)

; ===================================================================
; NHOM E1: XUAT CELL
; ===================================================================

; E1 - XUAT TEXT/DIM VAO CELL (CO HE SO FACTOR)
(defun C:E1 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	numlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Lay factor
					(setq factor (atof *E6-factor*))

					; Xu ly noi dung
					(setq textlist '() numlist '())
					(setq i 0)
					(repeat (sslength otcontents)
						(setq ent (ssname otcontents i))
						(setq obj (vlax-ename->vla-object ent))
						(setq content (GET-TEXT-CONTENT ent))

						; Kiem tra neu la dimension thi nhan factor
						(if (wcmatch (vla-get-objectname obj) "*Dimension*")
							(progn
								(setq num-val (* (vla-get-measurement obj) factor))
								(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
							)
							; Neu la text co chua m2 hoac m thi giu nguyen
							(if (or (vl-string-search "m2" content) (vl-string-search "m" content))
								(setq content content)
								; Neu la so thi nhan factor
								(progn
									(setq test-num (vl-catch-all-apply 'read (list content)))
									(if (and (not (vl-catch-all-error-p test-num)) (numberp test-num))
										(progn
											(setq num-val (* test-num factor))
											(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
										)
									)
								)
							)
						)

						(setq textlist (cons content textlist))
						(setq i (1+ i))
					)

					; Tao chuoi ket qua
					(setq textlist (reverse textlist))
					(if (= *E6-symbol* "+")
						(progn
							; Tao formula Excel
							(setq result-text "=")
							(setq first-item t)
							(foreach item textlist
								(if first-item
									(progn
										(setq result-text (strcat result-text item))
										(setq first-item nil)
									)
									(setq result-text (strcat result-text "+" item))
								)
							)
						)
						; Noi chuoi voi ky tu phan cach
						(progn
							(setq result-text (car textlist))
							(foreach item (cdr textlist)
								(setq result-text (strcat result-text *E6-symbol* item))
							)
						)
					)

					; Ghi vao Excel
					(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
					(vlax-put-property targetcell "Value" result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E11 - XUAT TEXT/DIM VOI HE SO NHAN THEM
(defun C:E11 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	coefficient
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Nhap he so nhan
					(if (not *last-coefficient*) (setq *last-coefficient* "1"))
					(setq coefficient (getreal (strcat "\nNhap he so nhan <" *last-coefficient* ">: ")))
					(if (not coefficient) (setq coefficient (atof *last-coefficient*)))
					(setq *last-coefficient* (rtos coefficient 2 3))

					; Lay factor
					(setq factor (atof *E6-factor*))

					; Xu ly noi dung
					(setq textlist '())
					(setq i 0)
					(repeat (sslength otcontents)
						(setq ent (ssname otcontents i))
						(setq obj (vlax-ename->vla-object ent))
						(setq content (GET-TEXT-CONTENT ent))

						; Kiem tra neu la dimension thi nhan factor
						(if (wcmatch (vla-get-objectname obj) "*Dimension*")
							(progn
								(setq num-val (* (vla-get-measurement obj) factor))
								(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
							)
							; Neu la text co chua m2 hoac m thi giu nguyen
							(if (or (vl-string-search "m2" content) (vl-string-search "m" content))
								(setq content content)
								; Neu la so thi nhan factor
								(progn
									(setq test-num (vl-catch-all-apply 'read (list content)))
									(if (and (not (vl-catch-all-error-p test-num)) (numberp test-num))
										(progn
											(setq num-val (* test-num factor))
											(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
										)
									)
								)
							)
						)

						(setq textlist (cons content textlist))
						(setq i (1+ i))
					)

					; Tao formula Excel voi he so nhan
					(setq textlist (reverse textlist))
					(setq result-text "=(")
					(setq first-item t)
					(foreach item textlist
						(if first-item
							(progn
								(setq result-text (strcat result-text item))
								(setq first-item nil)
							)
							(setq result-text (strcat result-text "+" item))
						)
					)
					(setq result-text (strcat result-text ")*" (rtos coefficient 2 3)))

					; Ghi vao Excel
					(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
					(vlax-put-property targetcell "Value" result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E12 - XUAT TEXT/DIM VAO CELL (TEXT GIU NGUYEN + DIM CO FACTOR)
(defun C:E12 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	result-text
	handlelist
	commenttext
	dwgpath
	dwgname
	factor
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Lay factor
					(setq factor (atof *E6-factor*))

					; Xu ly noi dung
					(setq textlist '())
					(setq i 0)
					(repeat (sslength otcontents)
						(setq ent (ssname otcontents i))
						(setq obj (vlax-ename->vla-object ent))
						(setq content (GET-TEXT-CONTENT ent))

						; Neu la dimension thi nhan factor, text giu nguyen
						(if (wcmatch (vla-get-objectname obj) "*Dimension*")
							(progn
								(setq num-val (* (vla-get-measurement obj) factor))
								(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
							)
							; Text/MText giu nguyen
							(setq content content)
						)

						(setq textlist (cons content textlist))
						(setq i (1+ i))
					)

					; Tao chuoi ket qua
					(setq textlist (reverse textlist))
					(if (= *E6-symbol* "+")
						(progn
							; Tao formula Excel
							(setq result-text "=")
							(setq first-item t)
							(foreach item textlist
								(if first-item
									(progn
										(setq result-text (strcat result-text item))
										(setq first-item nil)
									)
									(setq result-text (strcat result-text "+" item))
								)
							)
						)
						; Noi chuoi voi ky tu phan cach
						(progn
							(setq result-text (car textlist))
							(foreach item (cdr textlist)
								(setq result-text (strcat result-text *E6-symbol* item))
							)
						)
					)

					; Ghi vao Excel
					(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
					(vlax-put-property targetcell "Value" result-text)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E14 - DAT NHANH HE SO (FACTOR)=1
(defun C:E14 ( / )
	(setq *E6-factor* "1")
	(princ "\n=== FACTOR: 1 ===")
	(princ)
)

; E15 - DAT NHANH HE SO (FACTOR)=0.001
(defun C:E15 ( / )
	(setq *E6-factor* "0.001")
	(princ "\n=== FACTOR: 0.001 (mm -> m) ===")
	(princ)
)

; ===================================================================
; NHOM E2: XUAT COL/ROW/ARRAY
; ===================================================================

; Ham ho tro sap xep theo toa do
(defun SORT-BY-COORDINATES ( ss mode / entlist sorted-list)
	(setq entlist '())
	(setq i 0)
	(repeat (sslength ss)
		(setq ent (ssname ss i))
		(setq obj (vlax-ename->vla-object ent))
		(setq pt (vlax-get obj 'InsertionPoint))
		(setq entlist (cons (list ent (car pt) (cadr pt)) entlist))
		(setq i (1+ i))
	)

	; Sap xep theo mode
	(cond
		((= mode "COL") ; Cot: Y giam dan (tu tren xuong)
			(setq sorted-list (vl-sort entlist '(lambda (a b) (> (caddr a) (caddr b)))))
		)
		((= mode "ROW") ; Hang: X tang dan (tu trai qua phai)
			(setq sorted-list (vl-sort entlist '(lambda (a b) (< (cadr a) (cadr b)))))
		)
		((= mode "ARRAY") ; Mang: Y giam dan, roi X tang dan
			(setq sorted-list (vl-sort entlist '(lambda (a b)
				(if (equal (caddr a) (caddr b) (atof *E6-tolerance*))
					(< (cadr a) (cadr b))
					(> (caddr a) (caddr b))
				)
			)))
		)
		(t (setq sorted-list entlist))
	)

	; Tra ve danh sach entity
	(mapcar 'car sorted-list)
)

; Ham xu ly noi dung cho E2
(defun PROCESS-E2-CONTENT ( ent / obj content num-val factor)
	(setq obj (vlax-ename->vla-object ent))
	(setq content (GET-TEXT-CONTENT ent))
	(setq factor (atof *E6-factor*))

	; Neu Number = Y thi chi lay so
	(if (= *E6-number* "Y")
		(progn
			; Lay chi so tu text
			(setq content (EXTRACT-NUMBER content))
			; Nhan factor neu co so
			(if (and content (/= content ""))
				(progn
					(setq test-num (vl-catch-all-apply 'read (list content)))
					(if (and (not (vl-catch-all-error-p test-num)) (numberp test-num))
						(progn
							(setq num-val (* test-num factor))
							(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
						)
					)
				)
			)
		)
		; Neu Number = N thi xu ly binh thuong
		(progn
			; Kiem tra neu la dimension thi nhan factor
			(if (wcmatch (vla-get-objectname obj) "*Dimension*")
				(progn
					(setq num-val (* (vla-get-measurement obj) factor))
					(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
				)
				; Neu la text co chua m2 hoac m thi giu nguyen
				(if (or (vl-string-search "m2" content) (vl-string-search "m" content))
					(setq content content)
					; Neu la so thi nhan factor
					(progn
						(setq test-num (vl-catch-all-apply 'read (list content)))
						(if (and (not (vl-catch-all-error-p test-num)) (numberp test-num))
							(progn
								(setq num-val (* test-num factor))
								(setq content (rtos num-val 2 (if (= factor 1) 0 3)))
							)
						)
					)
				)
			)
		)
	)
	content
)

; E2 - XUAT THEO 3 CHE DO (COL/ROW/ARRAY)
(defun C:E2 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	mode
	sorted-ents
	textlist
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Chon che do
					(initget "1 2 3")
					(setq mode (getkword "\nChon che do [1. Cot/2. Hang/3. Mang]: "))

					(cond
						((= mode "1") (setq mode "COL"))
						((= mode "2") (setq mode "ROW"))
						((= mode "3") (setq mode "ARRAY"))
						(t (setq mode "COL"))
					)

					; Sap xep theo che do
					(setq sorted-ents (SORT-BY-COORDINATES otcontents mode))

					; Xu ly noi dung
					(setq textlist '())
					(foreach ent sorted-ents
						(setq content (PROCESS-E2-CONTENT ent))
						(setq textlist (cons content textlist))
					)
					(setq textlist (reverse textlist))

					; Ghi vao Excel theo che do
					(setq newrow startrow newcol startcol)
					(cond
						((= mode "COL") ; Cot
							(setq i 0)
							(foreach item textlist
								(setq targetcell (vlax-get-property xlcells "Cells" (+ startrow i) startcol))
								(vlax-put-property targetcell "Value" item)
								(setq i (1+ i))
							)
							(setq newrow (+ startrow i))
						)
						((= mode "ROW") ; Hang
							(setq i 0)
							(foreach item textlist
								(setq targetcell (vlax-get-property xlcells "Cells" startrow (+ startcol i)))
								(vlax-put-property targetcell "Value" item)
								(setq i (1+ i))
							)
							(setq newcol (+ startcol i))
						)
						((= mode "ARRAY") ; Mang - can xu ly phuc tap hon
							; Tam thoi dung che do cot
							(setq i 0)
							(foreach item textlist
								(setq targetcell (vlax-get-property xlcells "Cells" (+ startrow i) startcol))
								(vlax-put-property targetcell "Value" item)
								(setq i (1+ i))
							)
							(setq newrow (+ startrow i))
						)
					)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot
					(setq newrow (+ newrow (atoi *E6-jump*)) newcol newcol)
					(MOVE-CURSOR xlcells newrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa newcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E21 - XUAT NHANH RA COT
(defun C:E21 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	sorted-ents
	textlist
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Sap xep theo cot (Y giam dan)
					(setq sorted-ents (SORT-BY-COORDINATES otcontents "COL"))

					; Xu ly noi dung
					(setq textlist '())
					(foreach ent sorted-ents
						(setq content (PROCESS-E2-CONTENT ent))
						(setq textlist (cons content textlist))
					)
					(setq textlist (reverse textlist))

					; Ghi vao Excel theo cot
					(setq i 0)
					(foreach item textlist
						(setq targetcell (vlax-get-property xlcells "Cells" (+ startrow i) startcol))
						(vlax-put-property targetcell "Value" item)
						(setq i (1+ i))
					)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot xuong cuoi cot
					(setq newrow (+ startrow i (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E22 - XUAT NHANH RA HANG
(defun C:E22 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	sorted-ents
	textlist
	handlelist
	commenttext
	dwgpath
	dwgname
	newcol
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Sap xep theo hang (X tang dan)
					(setq sorted-ents (SORT-BY-COORDINATES otcontents "ROW"))

					; Xu ly noi dung
					(setq textlist '())
					(foreach ent sorted-ents
						(setq content (PROCESS-E2-CONTENT ent))
						(setq textlist (cons content textlist))
					)
					(setq textlist (reverse textlist))

					; Ghi vao Excel theo hang
					(setq i 0)
					(foreach item textlist
						(setq targetcell (vlax-get-property xlcells "Cells" startrow (+ startcol i)))
						(vlax-put-property targetcell "Value" item)
						(setq i (1+ i))
					)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot qua phai cuoi hang
					(setq newcol (+ startcol i))
					(MOVE-CURSOR xlcells startrow newcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa startrow) "," (itoa newcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E23 - XUAT NHANH RA MANG
(defun C:E23 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	sorted-ents
	textlist
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac text/mtext/dimension: ")
			(setq otcontents (ssget '((0 . "TEXT,MTEXT,DIMENSION,*DIMENSION"))))

			(if otcontents
				(progn
					; Sap xep theo mang
					(setq sorted-ents (SORT-BY-COORDINATES otcontents "ARRAY"))

					; Xu ly noi dung
					(setq textlist '())
					(foreach ent sorted-ents
						(setq content (PROCESS-E2-CONTENT ent))
						(setq textlist (cons content textlist))
					)
					(setq textlist (reverse textlist))

					; Ghi vao Excel theo mang (tam thoi dung cot)
					(setq i 0)
					(foreach item textlist
						(setq targetcell (vlax-get-property xlcells "Cells" (+ startrow i) startcol))
						(vlax-put-property targetcell "Value" item)
						(setq i (1+ i))
					)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq handlelist (GET-HANDLES otcontents))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Nhay chuot xuong cuoi mang
					(setq newrow (+ startrow i (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E24 - BAT NHANH NUMBER (Y)
(defun C:E24 ( / )
	(setq *E6-number* "Y")
	(princ "\n=== NUMBER: BAT ===")
	(princ)
)

; E25 - TAT NHANH NUMBER (N)
(defun C:E25 ( / )
	(setq *E6-number* "N")
	(princ "\n=== NUMBER: TAT ===")
	(princ)
)

; ===================================================================
; NHOM E3: GHI HANDLE
; ===================================================================

; E3 - CHI GHI HANDLE VAO COMMENT
(defun C:E3 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	handlelist
	commenttext
	dwgpath
	dwgname
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon cac doi tuong
			(princ "\nChon cac doi tuong: ")
			(setq otcontents (ssget))

			(if otcontents
				(progn
					; Lay thong tin file CAD
					(setq dwgpath (vla-get-fullname ActDoc))
					(setq dwgname (vl-filename-base dwgpath))

					; Lay handle va tao comment
					(setq handlelist (GET-HANDLES otcontents))
					(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))

					; Ghi comment vao Excel
					(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
					(WRITE-COMMENT-TO-CELL targetcell commenttext)

					; Nhay chuot
					(setq newrow (+ startrow (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Da ghi " (itoa (length handlelist)) " handle"))
				)
				(princ "\nKhong chon duoc doi tuong nao!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; Ham ho tro cho cac lenh handle
(defun ZTH-split-string (str delimiter / pos result temp)
	(setq result '())
	(setq temp str)

	(while (setq pos (vl-string-search delimiter temp))
		(if (> pos 0)
			(setq result (cons (substr temp 1 pos) result))
		)
		(setq temp (substr temp (+ pos (strlen delimiter) 1)))
	)

	; Them phan cuoi cung
	(if (and temp (/= temp ""))
		(setq result (cons temp result))
	)

	(reverse result)
)

(defun ZTH-clean-handle (handle-str / clean-str)
	(setq clean-str (vl-string-trim " \t\n\r" handle-str))

	; Loai bo dau ' o dau neu co
	(if (and (> (strlen clean-str) 0) (= (substr clean-str 1 1) "'"))
		(setq clean-str (substr clean-str 2))
	)

	; Loai bo cac ky tu khong phai hex (chi giu lai 0-9, A-F)
	(setq clean-str (ZTH-extract-hex-only clean-str))

	(vl-string-trim " \t\n\r" clean-str)
)

(defun ZTH-extract-hex-only (str / result i char)
	(setq result "")
	(setq i 1)

	(while (<= i (strlen str))
		(setq char (substr str i 1))
		(if (or (and (>= char "0") (<= char "9"))
				(and (>= (strcase char) "A") (<= (strcase char) "F")))
			(setq result (strcat result (strcase char)))
		)
		(setq i (1+ i))
	)

	result
)

(defun ZTH-extract-handles-from-text (text / handles i j temp-str char result)
	(setq handles '())
	(setq i 1)
	(setq text (strcase text))

	(while (<= i (- (strlen text) 5)) ; Can it nhat 6 ky tu
		(setq j i)
		(setq temp-str "")

		; Doc cac ky tu hex lien tiep
		(while (and (<= j (strlen text))
					(progn
						(setq char (substr text j 1))
						(or (and (>= char "0") (<= char "9"))
							(and (>= char "A") (<= char "F")))
					))
			(setq temp-str (strcat temp-str char))
			(setq j (1+ j))
		)

		; Neu co 6-8 ky tu hex thi co the la handle
		(if (and (>= (strlen temp-str) 6) (<= (strlen temp-str) 8))
			(setq handles (cons temp-str handles))
		)

		(setq i (1+ i))
	)

	; Loai bo trung lap
	(setq result '())
	(foreach handle handles
		(if (not (member handle result))
			(setq result (cons handle result))
		)
	)

	; Tra ve chuoi handle cach nhau boi dau ;
	(if result
		(apply 'strcat (reverse (cons (car result)
									  (mapcar '(lambda (x) (strcat ";" x)) (cdr result)))))
		""
	)
)

(defun ZTH-get-object-by-handle (handle-str / obj)
	(setq obj nil)

	(if (and handle-str (/= handle-str ""))
		(progn
			(vl-catch-all-apply
				'(lambda ()
					(setq obj (vlax-ename->vla-object (handent handle-str)))
				)
			)
		)
	)

	obj
)

; E31 - MO FILE CAD THEO COMMENT (HTC)
(defun C:E31 (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
			  file-path file-pos clean-path open-result open-result2 open-result3
			  acad-app acad-docs new-doc short-path)

	(princ "\nBat dau ket noi Excel...")

	; Ket noi den Excel
	(setq excel-app (vlax-get-or-create-object "Excel.Application"))

	(if excel-app
		(progn
			; Lay workbook va worksheet hien tai
			(setq excel-wb nil)
			(setq excel-ws nil)
			(setq active-cell nil)

			(vl-catch-all-apply
				'(lambda ()
					(setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
					(setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
					(setq active-cell (vlax-get-property excel-app "ActiveCell"))
				)
			)

			(if (not active-cell)
				(progn
					(princ "\nKhong the lay active cell tu Excel.")
					(vlax-release-object excel-app)
				)
				(progn
					; Lay comment tu cell
					(setq comment-obj nil)
					(setq comment-text "")

					(vl-catch-all-apply
						'(lambda ()
							(setq comment-obj (vlax-get-property active-cell "Comment"))
							(if comment-obj
								(setq comment-text (vlax-get-property comment-obj "Text"))
							)
						)
					)

					; Neu comment khong co duong dan, lay tu cell value
					(if (or (not comment-text) (= comment-text ""))
						(progn
							(setq cell-value nil)
							(setq value-result
								(vl-catch-all-apply
									'(lambda ()
										(setq cell-value (vlax-get-property active-cell "Value"))
									)
								)
							)

							(if (and (not (vl-catch-all-error-p value-result)) cell-value)
								(progn
									; Xu ly variant value
									(if (= (type cell-value) 'VARIANT)
										(setq cell-value (vlax-variant-value cell-value))
									)
									(setq cell-value (vl-princ-to-string cell-value))
									(princ (strcat "\nCell value: " cell-value))
									(setq comment-text cell-value)
								)
								(setq comment-text "")
							)
						)
					)

					; Tim duong dan file CAD
					(setq file-path (HTC-extract-file-path comment-text))

					(if (and file-path (/= file-path ""))
						(progn
							(princ (strcat "\nDuong dan file: " file-path))

							; Kiem tra file ton tai
							(if (findfile file-path)
								(progn
									(princ "\nFile ton tai, dang mo...")

									; Phuong phap 1: Dung VLA method (chinh)
									(setq open-result
										(vl-catch-all-apply
											'(lambda ()
												(setq acad-app (vlax-get-acad-object))
												(setq acad-docs (vlax-get-property acad-app "Documents"))
												(setq new-doc (vlax-invoke-method acad-docs "Open" file-path))

												; Chuyen sang document moi
												(if new-doc
													(progn
														; Cach 1: Set ActiveDocument
														(vl-catch-all-apply
															'(lambda ()
																(vlax-put-property acad-app "ActiveDocument" new-doc)
															)
														)

														; Cach 2: Activate document
														(vl-catch-all-apply
															'(lambda ()
																(vlax-invoke-method new-doc "Activate")
															)
														)

														; Cach 3: Set focus bang WindowState
														(vl-catch-all-apply
															'(lambda ()
																(vlax-put-property new-doc "WindowState" 3) ; acMax
															)
														)

														(princ "\nDa mo va chuyen sang file moi.")
													)
													(princ "\nDa mo file nhung khong chuyen duoc sang file moi.")
												)
											)
										)
									)

									; Neu VLA that bai, thu dung startapp
									(if (vl-catch-all-error-p open-result)
										(progn
											(princ "\nVLA method that bai, thu dung startapp...")
											(setq open-result2
												(vl-catch-all-apply
													'(lambda ()
														; Mo bang cach goi AutoCAD moi
														(startapp "acad.exe" file-path)
														; Doi 2 giay roi chuyen focus
														(command "delay" 2000)
														(princ "\nDa mo file bang startapp.")
													)
												)
											)

											; Neu startapp cung that bai, thu command don gian
											(if (vl-catch-all-error-p open-result2)
												(progn
													(princ "\nStartapp that bai, thu command don gian...")
													(setq open-result3
														(vl-catch-all-apply
															'(lambda ()
																; Chuyen doi duong dan ve dinh dang 8.3 neu co the
																(setq short-path (vl-filename-mktemp file-path))
																(if short-path
																	(command "_.open" short-path)
																	(command "_.open" file-path)
																)
															)
														)
													)
												)
											)
										)
									)
								)
								(princ (strcat "\nKhong tim thay file: " file-path))
							)
						)
						(princ "\nKhong tim thay duong dan file trong comment hoac cell.")
					)

					(vlax-release-object excel-app)
				)
			)
		)
		(princ "\nLoi: Khong the ket noi den Excel.")
	)

	(princ)
)

; Ham trich xuat duong dan file tu text
(defun HTC-extract-file-path (text / file-pos clean-path)
	(setq file-pos nil)
	(setq clean-path "")

	(if (and text (/= text ""))
		(progn
			; Tim vi tri "FileCad:"
			(setq file-pos (vl-string-search "FILECAD:" (strcase text)))

			(if file-pos
				(progn
					; Lay phan sau "FileCad:"
					(setq clean-path (substr text (+ file-pos 9))) ; 9 = length of "FileCad:" + 1

					; Loai bo khoang trang dau va cuoi
					(setq clean-path (vl-string-trim " \t\n\r" clean-path))

					; Loai bo cac ky tu xuong dong va khoang trang thua
					(setq clean-path (HTC-clean-file-path clean-path))

					(princ (strcat "\nDuong dan sau khi lam sach: " clean-path))
				)
				(setq clean-path "")
			)
		)
	)

	clean-path
)

; Ham lam sach duong dan file
(defun HTC-clean-file-path (path / result i char)
	(setq result "")
	(setq i 1)

	(while (<= i (strlen path))
		(setq char (substr path i 1))

		; Chi giu lai cac ky tu hop le cho duong dan
		(if (or (and (>= char "A") (<= char "Z"))
				(and (>= char "a") (<= char "z"))
				(and (>= char "0") (<= char "9"))
				(= char "\\")
				(= char "/")
				(= char ":")
				(= char ".")
				(= char "_")
				(= char "-")
				(= char " ")
				(= char "#")
				(= char "(")
				(= char ")"))
			(setq result (strcat result char))
		)

		(setq i (1+ i))
	)

	; Loai bo khoang trang dau va cuoi lan nua
	(vl-string-trim " \t\n\r" result)
)

; E32 - ZOOM VA HIGHLIGHT DOI TUONG THEO HANDLE (ZTH)
(defun C:E32 (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
			  handle-text handle-list clean-handle obj found-count zoom-objects
			  min-pt max-pt i file-pos)

	; Ket noi den Excel
	(setq excel-app (vlax-get-or-create-object "Excel.Application"))

	(if excel-app
		(progn
			; Lay workbook va worksheet hien tai
			(setq excel-wb nil)
			(setq excel-ws nil)
			(setq active-cell nil)

			(vl-catch-all-apply
				'(lambda ()
					(setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
					(setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
					(setq active-cell (vlax-get-property excel-app "ActiveCell"))
				)
			)

			(if (not active-cell)
				(progn
					(princ "\nKhong the lay active cell tu Excel.")
					(vlax-release-object excel-app)
				)
				(progn
					; Lay comment tu cell
					(setq comment-obj nil)
					(setq comment-text "")

					(vl-catch-all-apply
						'(lambda ()
							(setq comment-obj (vlax-get-property active-cell "Comment"))
							(if comment-obj
								(setq comment-text (vlax-get-property comment-obj "Text"))
							)
						)
					)

					; Neu khong co comment, lay tu cell value
					(if (or (not comment-text) (= comment-text ""))
						(progn
							(setq cell-value nil)
							(vl-catch-all-apply
								'(lambda ()
									(setq cell-value (vlax-get-property active-cell "Value"))
									(if (= (type cell-value) 'VARIANT)
										(setq cell-value (vlax-variant-value cell-value))
									)
									(setq cell-value (vl-princ-to-string cell-value))
									(setq comment-text cell-value)
								)
							)
						)
					)

					; Xu ly comment text - loai bo phan FileCad neu co
					(if (and comment-text (/= comment-text ""))
						(progn
							; Tim vi tri cua FileCad hoac \n de cat chuoi
							(setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
							(if (not file-pos)
								(setq file-pos (vl-string-search "\n" comment-text))
							)

							(if file-pos
								(setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
								(setq handle-text (vl-string-trim " \t\n\r" comment-text))
							)

							; Neu van chua co handle, thu tim trong toan bo comment
							(if (or (not handle-text) (= handle-text ""))
								(setq handle-text (ZTH-extract-handles-from-text comment-text))
							)
						)
						(setq handle-text "")
					)

					; Xu ly handle
					(if (and handle-text (/= handle-text ""))
						(progn
							(princ (strcat "\nHandle text: " handle-text))

							; Tach chuoi handle theo dau ";"
							(setq handle-list (ZTH-split-string handle-text ";"))
							(setq found-count 0)
							(setq zoom-objects '())

							; Kiem tra tung handle
							(foreach handle handle-list
								(setq clean-handle (ZTH-clean-handle handle))
								(if (and clean-handle (/= clean-handle ""))
									(progn
										(setq obj (ZTH-get-object-by-handle clean-handle))
										(if obj
											(progn
												(setq found-count (1+ found-count))
												(setq zoom-objects (cons obj zoom-objects))
												(princ (strcat "\nTim thay doi tuong: " clean-handle))
											)
											(princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
										)
									)
								)
							)

							; Zoom va highlight
							(if (> found-count 0)
								(progn
									; Tinh toan bounding box
									(setq min-pt nil max-pt nil)
									(foreach obj zoom-objects
										(vl-catch-all-apply
											'(lambda ()
												(setq temp-min (vlax-get obj 'BoundingBoxMin))
												(setq temp-max (vlax-get obj 'BoundingBoxMax))
												(if (not min-pt)
													(progn
														(setq min-pt temp-min)
														(setq max-pt temp-max)
													)
													(progn
														(setq min-pt (list (min (car min-pt) (car temp-min))
																		   (min (cadr min-pt) (cadr temp-min))
																		   (min (caddr min-pt) (caddr temp-min))))
														(setq max-pt (list (max (car max-pt) (car temp-max))
																		   (max (cadr max-pt) (cadr temp-max))
																		   (max (caddr max-pt) (caddr temp-max))))
													)
												)
											)
										)
									)

									; Zoom den vung
									(if (and min-pt max-pt)
										(progn
											(command "_.zoom" "_window" min-pt max-pt)
											; Highlight cac doi tuong
											(foreach obj zoom-objects
												(vl-catch-all-apply
													'(lambda ()
														(vlax-put obj 'Highlight :vlax-true)
													)
												)
											)
										)
									)

									(princ (strcat "\nHoan thanh! Da zoom va highlight " (itoa found-count) " doi tuong."))
								)
								(princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
							)

							(vlax-release-object excel-app)
						)
					)
				)
			)
		)
		(princ "\nLoi: Khong the ket noi den Excel.")
	)

	(princ)
)

; E33 - ZOOM VA SELECT DOI TUONG THEO HANDLE (STH)
(defun C:E33 (/ excel-app excel-wb excel-ws active-cell comment-obj comment-text cell-value
			  handle-text handle-list clean-handle obj found-count zoom-objects select-objects
			  min-pt max-pt i file-pos all-min-pt all-max-pt temp-min temp-max ent ss)

	; Ket noi den Excel
	(setq excel-app (vlax-get-or-create-object "Excel.Application"))

	(if excel-app
		(progn
			; Lay workbook va worksheet hien tai
			(setq excel-wb nil)
			(setq excel-ws nil)
			(setq active-cell nil)

			(vl-catch-all-apply
				'(lambda ()
					(setq excel-wb (vlax-get-property excel-app "ActiveWorkbook"))
					(setq excel-ws (vlax-get-property excel-app "ActiveSheet"))
					(setq active-cell (vlax-get-property excel-app "ActiveCell"))
				)
			)

			(if (not active-cell)
				(progn
					(princ "\nKhong the lay active cell tu Excel.")
					(vlax-release-object excel-app)
				)
				(progn
					; Lay comment tu cell
					(setq comment-obj nil)
					(setq comment-text "")

					(vl-catch-all-apply
						'(lambda ()
							(setq comment-obj (vlax-get-property active-cell "Comment"))
							(if comment-obj
								(setq comment-text (vlax-get-property comment-obj "Text"))
							)
						)
					)

					; Neu khong co comment, lay tu cell value
					(if (or (not comment-text) (= comment-text ""))
						(progn
							(setq cell-value nil)
							(vl-catch-all-apply
								'(lambda ()
									(setq cell-value (vlax-get-property active-cell "Value"))
									(if (= (type cell-value) 'VARIANT)
										(setq cell-value (vlax-variant-value cell-value))
									)
									(setq cell-value (vl-princ-to-string cell-value))
									(setq comment-text cell-value)
								)
							)
						)
					)

					; Xu ly comment text - loai bo phan FileCad neu co
					(if (and comment-text (/= comment-text ""))
						(progn
							; Tim vi tri cua FileCad hoac \n de cat chuoi
							(setq file-pos (vl-string-search "FILECAD:" (strcase comment-text)))
							(if (not file-pos)
								(setq file-pos (vl-string-search "\n" comment-text))
							)

							(if file-pos
								(setq handle-text (vl-string-trim " \t\n\r" (substr comment-text 1 file-pos)))
								(setq handle-text (vl-string-trim " \t\n\r" comment-text))
							)

							; Neu van chua co handle, thu tim trong toan bo comment
							(if (or (not handle-text) (= handle-text ""))
								(setq handle-text (ZTH-extract-handles-from-text comment-text))
							)
						)
						(setq handle-text "")
					)

					; Xu ly handle
					(if (and handle-text (/= handle-text ""))
						(progn
							(princ (strcat "\nHandle text: " handle-text))

							; Tach chuoi handle theo dau ";"
							(setq handle-list (ZTH-split-string handle-text ";"))
							(setq found-count 0)
							(setq zoom-objects '())
							(setq select-objects '())

							; Kiem tra tung handle
							(foreach handle handle-list
								(setq clean-handle (ZTH-clean-handle handle))
								(if (and clean-handle (/= clean-handle ""))
									(progn
										(setq obj (ZTH-get-object-by-handle clean-handle))
										(if obj
											(progn
												(setq found-count (1+ found-count))
												(setq zoom-objects (cons obj zoom-objects))
												(setq ent (vlax-vla-object->ename obj))
												(if ent
													(setq select-objects (cons ent select-objects))
												)
												(princ (strcat "\nTim thay doi tuong: " clean-handle))
											)
											(princ (strcat "\nKhong tim thay doi tuong: " clean-handle))
										)
									)
								)
							)

							; Zoom va select
							(if (> found-count 0)
								(progn
									; Tao selection set
									(if select-objects
										(progn
											(setq ss (ssadd))
											(foreach ent select-objects
												(ssadd ent ss)
											)
											; Select cac doi tuong
											(sssetfirst nil ss)
										)
									)

									; Tinh toan bounding box cho zoom
									(setq all-min-pt nil all-max-pt nil)
									(foreach obj zoom-objects
										(vl-catch-all-apply
											'(lambda ()
												(setq temp-min (vlax-get obj 'BoundingBoxMin))
												(setq temp-max (vlax-get obj 'BoundingBoxMax))
												(if (not all-min-pt)
													(progn
														(setq all-min-pt temp-min)
														(setq all-max-pt temp-max)
													)
													(progn
														(setq all-min-pt (list (min (car all-min-pt) (car temp-min))
																			   (min (cadr all-min-pt) (cadr temp-min))
																			   (min (caddr all-min-pt) (caddr temp-min))))
														(setq all-max-pt (list (max (car all-max-pt) (car temp-max))
																			   (max (cadr all-max-pt) (cadr temp-max))
																			   (max (caddr all-max-pt) (caddr temp-max))))
													)
												)
											)
										)
									)

									; Zoom den vung
									(if (and all-min-pt all-max-pt)
										(command "_.zoom" "_window" all-min-pt all-max-pt)
									)

									(princ (strcat "\nHoan thanh! Da zoom va select " (itoa found-count) " doi tuong."))
								)
								(princ "\nKhong tim thay doi tuong nao voi cac handle da cung cap.")
							)

							(vlax-release-object excel-app)
						)
					)
				)
			)
		)
		(princ "\nLoi: Khong the ket noi den Excel.")
	)

	(princ)
)

; E34 - BAT NHANH HANDLE (Y)
(defun C:E34 ( / )
	(setq *E6-handle* "Y")
	(princ "\n=== HANDLE: BAT ===")
	(princ)
)

; E35 - TAT NHANH HANDLE (N)
(defun C:E35 ( / )
	(setq *E6-handle* "N")
	(princ "\n=== HANDLE: TAT ===")
	(princ)
)

; ===================================================================
; NHOM E4: XUAT TABLE
; ===================================================================

; Ham ho tro cho table
(defun SETCELLTEXT ( xlcells row col text)
	(setq targetcell (vlax-get-property xlcells "Cells" row col))
	(vlax-put-property targetcell "Value" text)
)

; E4 - XUAT BANG THEO LINE (tuong duong E5 cu)
(defun C:E4 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	otcontents
	textlist
	lpxlist
	lpylist
	blocklist
	linelist
	newrow
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon bang
			(princ "\nChon bang de xuat: ")
			(setq otcontents (ssget))

			(if otcontents
				(progn
					; Khoi tao danh sach
					(setq textlist '() lpxlist '() lpylist '() blocklist '() linelist '())

					; Xu ly thong tin bang don gian
					(setq i 0)
					(repeat (sslength otcontents)
						(setq ent (ssname otcontents i))
						(setq obj (vlax-ename->vla-object ent))
						(setq objtype (vla-get-objectname obj))

						(cond
							((or (= objtype "AcDbText") (= objtype "AcDbMText"))
								(setq textlist (cons ent textlist))
							)
						)
						(setq i (1+ i))
					)

					; Sap xep text theo toa do va xuat
					(if textlist
						(progn
							; Sap xep theo Y giam dan, roi X tang dan
							(setq sorted-textlist '())
							(foreach txt textlist
								(setq obj (vlax-ename->vla-object txt))
								(setq pt (vlax-get obj 'InsertionPoint))
								(setq content (GET-TEXT-CONTENT txt))
								(setq sorted-textlist (cons (list content (car pt) (cadr pt)) sorted-textlist))
							)

							; Sap xep
							(setq sorted-textlist (vl-sort sorted-textlist '(lambda (a b)
								(if (equal (caddr a) (caddr b) (atof *E6-tolerance*))
									(< (cadr a) (cadr b))
									(> (caddr a) (caddr b))
								)
							)))

							; Xuat theo hang
							(setq current-row startrow)
							(setq current-col startcol)
							(setq last-y (caddr (car sorted-textlist)))

							(foreach item sorted-textlist
								(setq content (car item))
								(setq x (cadr item))
								(setq y (caddr item))

								; Neu Y khac nhau thi xuong hang moi
								(if (not (equal y last-y (atof *E6-tolerance*)))
									(progn
										(setq current-row (1+ current-row))
										(setq current-col startcol)
										(setq last-y y)
									)
								)

								; Ghi vao Excel
								(SETCELLTEXT xlcells current-row current-col content)
								(setq current-col (1+ current-col))
							)

							; Ghi handle neu can
							(if (= *E6-handle* "Y")
								(progn
									(setq dwgpath (vla-get-fullname ActDoc))
									(setq dwgname (vl-filename-base dwgpath))
									(setq handlelist (GET-HANDLES otcontents))
									(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
									(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
									(WRITE-COMMENT-TO-CELL targetcell commenttext)
								)
							)

							; Nhay chuot
							(setq newrow (+ current-row (atoi *E6-jump*)))
							(MOVE-CURSOR xlcells newrow startcol)
							(princ (strcat "\nHoan thanh! Chuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
						)
						(princ "\nKhong tim thay text nao trong selection!")
					)
				)
				(princ "\nKhong chon duoc bang!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	; Don dep
	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E41 - XUAT BANG THEO LINE (GIU FORMAT BANG) - CTE
(defun C:E41 ( /
	CheckActiveCell
	CheckRun
	FullNameFileXlsx
	GroupListAddress
	ListCoordinateX
	ListCoordinateY
	ListDataLineX
	ListDataLineY
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaLayerLock
	ListVlaObjectLine
	ListVlaObjectDelete
	ListVlaObjectText
	SeparatorCSV
	ToleranceValue
	VlaDrawingCurrent)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(setq ToleranceValue 1e-8)
	(CAEX_SET_VARSYSTEM_C2E)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(setq CheckRun (CAEX_CHECKRUN))
				(if CheckRun
					(progn
						(setq ListTemp (CAEX_GET_LISTDATALINE_FROM_CAD))
						(setq ListDataLineX (nth 0 ListTemp))
						(setq ListDataLineY (nth 1 ListTemp))
						(setq ListCoordinateX (mapcar 'car ListDataLineX))
						(setq ListCoordinateY (mapcar 'car ListDataLineY))

						(setq GroupListAddress (CAEX_GET_GROUPLISTADDRESS_FROM_CAD))
						(setq ListDataTable (CAEX_GET_LISTDATATABLE_FROM_CAD))
						(CAEX_CREATE_TABLE_FOR_EXCEL)
					)
				)
			)
		)
	)))

	(mapcar 'vla-delete ListVlaObjectDelete)
	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)

; E42 - XUAT BANG THEO TABLE - TE
(defun C:E42 ( /
	ActDoc
	*Space*
	xlapp
	xlcells
	startrow
	startcol
	table-ent
	table-obj
	rows
	cols
	row
	col
	cell-text
	newrow
	handlelist
	commenttext
	dwgpath
	dwgname
	oerror)

	(vl-load-com)
	(setq oerror *error*)
	(defun *error* ( msg )
		(princ (strcat "\n<" msg ">\n"))
		(setq *error* oerror)
		(vla-EndUndoMark ActDoc)
		(princ)
	)

	(setq ActDoc (vla-get-activedocument (vlax-get-acad-object))
		*Space* (vlax-get-property ActDoc (nth (vla-get-ActiveSpace ActDoc) '("PaperSpace" "ModelSpace"))))

	(vla-EndUndoMark ActDoc)
	(vla-StartUndoMark ActDoc)

	; Ket noi Excel
	(setq excel-data (CONNECT-EXCEL))
	(setq xlapp (nth 0 excel-data)
		  xlcells (nth 1 excel-data)
		  startrow (nth 2 excel-data)
		  startcol (nth 3 excel-data))

	(if excel-data
		(progn
			; Chon table CAD
			(princ "\nChon table CAD: ")
			(setq table-ent (car (entsel)))

			(if (and table-ent (= (cdr (assoc 0 (entget table-ent))) "ACAD_TABLE"))
				(progn
					(setq table-obj (vlax-ename->vla-object table-ent))
					(setq rows (vlax-get table-obj 'Rows))
					(setq cols (vlax-get table-obj 'Columns))

					; Xuat tung cell
					(setq row 0)
					(repeat rows
						(setq col 0)
						(repeat cols
							(setq cell-text "")
							(vl-catch-all-apply
								'(lambda ()
									(setq cell-text (vlax-invoke table-obj 'GetText row col))
									; Xu ly MTEXT format codes
									(setq cell-text (CLEAN-MTEXT cell-text))
									(setq cell-text (CONVERT-SPECIAL-CHARS cell-text))
								)
							)

							; Ghi vao Excel
							(SETCELLTEXT xlcells (+ startrow row) (+ startcol col) cell-text)
							(setq col (1+ col))
						)
						(setq row (1+ row))
					)

					; Ghi handle neu can
					(if (= *E6-handle* "Y")
						(progn
							(setq dwgpath (vla-get-fullname ActDoc))
							(setq dwgname (vl-filename-base dwgpath))
							(setq ss (ssadd))
							(ssadd table-ent ss)
							(setq handlelist (GET-HANDLES ss))
							(setq commenttext (CREATE-COMMENT-TEXT handlelist dwgpath dwgname))
							(setq targetcell (vlax-get-property xlcells "Cells" startrow startcol))
							(WRITE-COMMENT-TO-CELL targetcell commenttext)
						)
					)

					; Dong khung neu can
					(if (= *E6-frame* "Y")
						(progn
							; Dong khung don gian
							(setq range (vlax-get-property xlcells "Range"
								(vlax-get-property xlcells "Cells" startrow startcol)
								(vlax-get-property xlcells "Cells" (+ startrow rows -1) (+ startcol cols -1))))
							(setq borders (vlax-get-property range "Borders"))
							(vlax-put-property borders "LineStyle" 1)
						)
					)

					; Nhay chuot xuong duoi table
					(setq newrow (+ startrow rows (atoi *E6-jump*)))
					(MOVE-CURSOR xlcells newrow startcol)
					(princ (strcat "\nHoan thanh! Da xuat table " (itoa rows) "x" (itoa cols) " sang Excel"))
					(princ (strcat "\nChuot dang o vi tri " (itoa newrow) "," (itoa startcol)))
				)
				(princ "\nKhong phai table CAD hoac khong chon duoc!")
			)
		)
		(princ "\nKhong the ket noi Excel!")
	)

	(setq *error* oerror)
	(vla-EndUndoMark ActDoc)
	(princ)
)

; E43 - XUAT BANG TU EXCEL QUA CAD - ETC
(defun C:E43 ( /
	CheckActiveCell
	ListDataCoordinateX
	ListDataCoordinateY
	ListDataLine
	ListDataTable
	ListTemp
	ListVarSystem_OldValue
	ListVlaObjectOfTable
	NodeTotalX
	NodeTotalY
	ScaleGlobal
	ScaleTableTemp
	VlaDrawingCurrent
	VlaSpace)

	(vl-load-com)
	(setq VlaDrawingCurrent (vla-get-activedocument (vlax-get-acad-object)))
	(vla-startundomark VlaDrawingCurrent)
	(CAEX_SET_VARSYSTEM_E2C)
	(CAEX_CREATE_LISTVLALAYERLOCK)

	(vl-catch-all-apply (function (lambda ( / )
		(setq CheckActiveCell (CAEX_CHECKACTIVECELL))
		(if CheckActiveCell
			(progn
				(if
					(not
						(and
							(or
								(= (type ScaleTable) 'INT)
								(= (type ScaleTable) 'REAL)
							)
							(> ScaleTable 0)
						)
					)
					(setq ScaleTable 1.0)
				)
				(initget 4)
				(setq ScaleTableTemp (getreal (strcat "\nEnter the scale of table <" (rtos ScaleTable 2) ">:")))
				(if ScaleTableTemp
					(setq ScaleTable ScaleTableTemp)
				)
				(if (= (getvar "CVPORT") 1)
					(setq VlaSpace (vla-get-PaperSpace VlaDrawingCurrent))
					(setq VlaSpace (vla-get-ModelSpace VlaDrawingCurrent))
				)

				(setq ListTemp (CAEX_GET_LISTDATATABLE_FROM_EXCEL Nil Nil))
				(setq ListDataTable (nth 0 ListTemp))
				(setq ListDataCoordinateX (nth 1 ListTemp))
				(setq ListDataCoordinateY (nth 2 ListTemp))

				(setq NodeTotalX (- (length ListDataCoordinateX) 1))
				(setq NodeTotalY (- (length ListDataCoordinateY) 1))
				(setq ScaleGlobal (* (/ 2.5 (CAEX_FIND_VALUE_POPULAR (mapcar '(lambda (x) (nth 1 (assoc 6 x))) ListDataTable))) ScaleTable))

				(setq ListDataLine (CAEX_GET_LISTDATALINE_FROM_EXCEL))
				(setq ListVlaObjectOfTable (CAEX_CREATE_TABLE_FOR_CAD))
				(CAEX_PICK_POINT_FOR_TABLE)
			)
			(princ "\nData from excel could not be found!")
		)
	)))

	(CAEX_RESTORE_LOCK_LAYER)
	(CAEX_RESET_VARSYSTEM)
	(vla-endundomark VlaDrawingCurrent)
	(princ)
)

; E44 - CONG TAC MO FRAME (Y/N)
(defun C:E44 ( / )
	; Dao trang thai Frame
	(if (= *E6-frame* "Y")
		(progn
			(setq *E6-frame* "N")
			(princ "\n=== FRAME: TAT ===")
		)
		(progn
			(setq *E6-frame* "Y")
			(princ "\n=== FRAME: BAT ===")
		)
	)
	(princ)
)

; ===================================================================
; E0 - THIET LAP TOAN CUC (SETTINGS)
; ===================================================================

(defun C:E0 ( / choice)
	(princ "\n=== THIET LAP CAD TO EXCEL V3.0 ===")
	(princ (strcat "\nHandle: " *E6-handle*))
	(princ (strcat "\nFrame: " *E6-frame*))
	(princ (strcat "\nNumber: " *E6-number*))
	(princ (strcat "\nSymbol: " *E6-symbol*))
	(princ (strcat "\nFactor: " *E6-factor*))
	(princ (strcat "\nJump: " *E6-jump*))
	(princ (strcat "\nTolerance: " *E6-tolerance*))

	(princ "\n\nSu dung cac lenh nhanh:")
	(princ "\nE14/E15 - Thay doi Factor")
	(princ "\nE24/E25 - Thay doi Number")
	(princ "\nE34/E35 - Thay doi Handle")
	(princ "\nE44 - Thay doi Frame")

	; Tuy chon thay doi Symbol
	(initget "S")
	(setq choice (getkword "\nThay doi Symbol? [S/Enter de thoat]: "))
	(if (= choice "S")
		(progn
			(setq new-symbol (getstring "\nNhap Symbol moi: "))
			(if new-symbol
				(progn
					(setq *E6-symbol* new-symbol)
					(princ (strcat "\nSymbol da duoc thay doi thanh: " *E6-symbol*))
				)
			)
		)
	)

	; Tuy chon thay doi Factor
	(initget "F")
	(setq choice (getkword "\nThay doi Factor? [F/Enter de thoat]: "))
	(if (= choice "F")
		(progn
			(setq new-factor (getreal "\nNhap Factor moi: "))
			(if new-factor
				(progn
					(setq *E6-factor* (rtos new-factor 2 6))
					(princ (strcat "\nFactor da duoc thay doi thanh: " *E6-factor*))
				)
			)
		)
	)
			(write-line (strcat dialog " : dialog {") dch)
			(write-line "  label = \"Thiet lap CAD to Excel v3.0\";" dch)
			(write-line "  : column {" dch)
			(write-line "    : boxed_column {" dch)
			(write-line "      label = \"Cac thiet lap\";" dch)
			(write-line "      : row {" dch)
			(write-line "        : toggle {" dch)
			(write-line "          key = \"handle\";" dch)
			(write-line "          label = \"Ghi Handle vao Comment\";" dch)
			(write-line "        }" dch)
			(write-line "        : toggle {" dch)
			(write-line "          key = \"frame\";" dch)
			(write-line "          label = \"Ve khung table\";" dch)
			(write-line "        }" dch)
			(write-line "        : toggle {" dch)
			(write-line "          key = \"number\";" dch)
			(write-line "          label = \"Chi lay so (Number mode)\";" dch)
			(write-line "        }" dch)
			(write-line "      }" dch)
			(write-line "      : row {" dch)
			(write-line "        : edit_box {" dch)
			(write-line "          key = \"symbol\";" dch)
			(write-line "          label = \"Ky tu noi (Symbol):\";" dch)
			(write-line "          edit_width = 10;" dch)
			(write-line "        }" dch)
			(write-line "        : edit_box {" dch)
			(write-line "          key = \"factor\";" dch)
			(write-line "          label = \"He so (Factor):\";" dch)
			(write-line "          edit_width = 10;" dch)
			(write-line "        }" dch)
			(write-line "      }" dch)
			(write-line "      : row {" dch)
			(write-line "        : edit_box {" dch)
			(write-line "          key = \"jump\";" dch)
			(write-line "          label = \"So hang nhay (Jump):\";" dch)
			(write-line "          edit_width = 10;" dch)
			(write-line "        }" dch)
			(write-line "        : edit_box {" dch)
			(write-line "          key = \"tolerance\";" dch)
			(write-line "          label = \"Dung sai (Tolerance):\";" dch)
			(write-line "          edit_width = 10;" dch)
			(write-line "        }" dch)
			(write-line "      }" dch)
			(write-line "    }" dch)
			(write-line "    : row {" dch)
			(write-line "      fixed_width = true;" dch)
			(write-line "      alignment = centered;" dch)
			(write-line "      : button {" dch)
			(write-line "        key = \"accept\";" dch)
			(write-line "        label = \"OK\";" dch)
			(write-line "        is_default = true;" dch)
			(write-line "        fixed_width = true;" dch)
			(write-line "        width = 12;" dch)
			(write-line "      }" dch)
			(write-line "      : button {" dch)
			(write-line "        key = \"cancel\";" dch)
			(write-line "        label = \"Cancel\";" dch)
			(write-line "        is_cancel = true;" dch)
			(write-line "        fixed_width = true;" dch)
			(write-line "        width = 12;" dch)
			(write-line "      }" dch)
			(write-line "    }" dch)
			(write-line "  }" dch)
			(write-line "}" dch)
			(close dch)

			; Load va hien thi dialog
			(if (not (load_dialog dcl))
				(princ "\nKhong the load dialog!")
				(progn
					(if (not (new_dialog dialog dcl))
						(princ "\nKhong the tao dialog!")
						(progn
							; Set gia tri hien tai
							(set_tile "handle" (if (= *E6-handle* "Y") "1" "0"))
							(set_tile "frame" (if (= *E6-frame* "Y") "1" "0"))
							(set_tile "number" (if (= *E6-number* "Y") "1" "0"))
							(set_tile "symbol" *E6-symbol*)
							(set_tile "factor" *E6-factor*)
							(set_tile "jump" *E6-jump*)
							(set_tile "tolerance" *E6-tolerance*)

							; Action cho cac nut
							(action_tile "accept" "(setq flag 1)(done_dialog)")
							(action_tile "cancel" "(setq flag 0)(done_dialog)")

							; Hien thi dialog
							(setq flag (start_dialog))
							(unload_dialog dcl)

							; Xu ly ket qua
							(if (= flag 1)
								(progn
									; Luu cac gia tri moi
									(setq *E6-handle* (if (= (get_tile "handle") "1") "Y" "N"))
									(setq *E6-frame* (if (= (get_tile "frame") "1") "Y" "N"))
									(setq *E6-number* (if (= (get_tile "number") "1") "Y" "N"))
									(setq *E6-symbol* (get_tile "symbol"))
									(setq *E6-factor* (get_tile "factor"))
									(setq *E6-jump* (get_tile "jump"))
									(setq *E6-tolerance* (get_tile "tolerance"))

									; Hien thi thong bao
									(princ "\n=== CAC THIET LAP DA DUOC LUU ===")
									(princ (strcat "\nHandle: " *E6-handle*))
									(princ (strcat "\nFrame: " *E6-frame*))
									(princ (strcat "\nNumber: " *E6-number*))
									(princ (strcat "\nSymbol: " *E6-symbol*))
									(princ (strcat "\nFactor: " *E6-factor*))
									(princ (strcat "\nJump: " *E6-jump*))
									(princ (strcat "\nTolerance: " *E6-tolerance*))
								)
								(princ "\nHuy thiet lap.")
							)
						)
					)
				)
			)
			; Xoa file DCL tam
			(vl-file-delete dcl)
		)
	)
	(princ)
)

; ===================================================================
; THONG BAO LOAD THANH CONG
; ===================================================================

(princ "\n╔══════════════════════════════════════════════════════════════╗")
(princ "\n║              CAD TO EXCEL - PHIEN BAN 3.0 TICH HOP          ║")
(princ "\n║                     Tac gia: Zit Đại Ka                     ║")
(princ "\n║                   Ngay: 20/12/2024                          ║")
(princ "\n╠══════════════════════════════════════════════════════════════╣")
(princ "\n║ NHOM E1 (XUAT CELL):                                         ║")
(princ "\n║  E1, E11, E12, E14, E15                                      ║")
(princ "\n║ NHOM E2 (XUAT COL/ROW/ARRAY):                                ║")
(princ "\n║  E2, E21, E22, E23, E24, E25                                 ║")
(princ "\n║ NHOM E3 (GHI HANDLE):                                        ║")
(princ "\n║  E3, E31, E32, E33, E34, E35                                 ║")
(princ "\n║ NHOM E4 (XUAT TABLE):                                        ║")
(princ "\n║  E4, E41, E42, E43, E44                                      ║")
(princ "\n║ THIET LAP:                                                   ║")
(princ "\n║  E0 - Mo bang thiet lap toan cuc                            ║")
(princ "\n╚══════════════════════════════════════════════════════════════╝")
(princ "\n=== Z-CAD2EXCEL-E1_V3 LOADED SUCCESSFULLY ===")

; ===================================================================
; CAC HAM HO TRO CHO CTE VA ETC
; ===================================================================

; Ham kiem tra active cell trong Excel
(defun CAEX_CHECKACTIVECELL ( /
	CheckActiveCell
	CheckCloseApp
	VlaAppExcel
	VlaRangeActive
	VlaWorkbooks)

	(setq VlaAppExcel (vlax-get-Object "Excel.Application"))
	(if VlaAppExcel
		(progn
			(setq VlaRangeActive (vlax-get-property VlaAppExcel "Selection"))
			(if VlaRangeActive
				(setq CheckActiveCell T)
			)

			(setq VlaWorkbooks (vlax-get-property VlaAppExcel "Workbooks"))
			(setq CheckCloseApp (= (vla-get-count VlaWorkbooks) 0))
			(if CheckCloseApp
				(progn
					(vlax-invoke-method VlaAppExcel "Quit")
					(vlax-release-object VlaAppExcel)
				)
			)
		)
	)
	CheckActiveCell
)

; Ham kiem tra run
(defun CAEX_CHECKRUN ( / CheckRun)
	(setq CheckRun T)
	CheckRun
)

; Ham tao danh sach layer lock
(defun CAEX_CREATE_LISTVLALAYERLOCK ( / )
	(setq ListVlaLayerLock '())
)

; Ham khoi phuc layer lock
(defun CAEX_RESTORE_LOCK_LAYER ( / )
	(foreach VlaLayerLock ListVlaLayerLock
		(vl-catch-all-error-p (vl-catch-all-apply 'vla-put-Lock (list VlaLayerLock :vlax-true)))
	)
)

; Ham set bien he thong cho C2E
(defun CAEX_SET_VARSYSTEM_C2E ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in cad to excel..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)

; Ham set bien he thong cho E2C
(defun CAEX_SET_VARSYSTEM_E2C ( / Temp VarSystem)
	(foreach Temp (list (list "CMDECHO" 0) (list "DIMZIN" 8) (list "MODEMACRO" "Export table in excel to cad..."))
		(setq VarSystem (car Temp))
		(setq ListVarSystem_OldValue (cons (list VarSystem (getvar VarSystem)) ListVarSystem_OldValue))
		(setvar VarSystem (cadr Temp))
	)
)

; Ham reset bien he thong
(defun CAEX_RESET_VARSYSTEM ( / Temp VarSystem)
	(foreach Temp ListVarSystem_OldValue
		(setvar (car Temp) (cadr Temp))
	)
)

; Ham lay du lieu line tu CAD
(defun CAEX_GET_LISTDATALINE_FROM_CAD ( / )
	(list '() '())
)

; Ham lay group list address tu CAD
(defun CAEX_GET_GROUPLISTADDRESS_FROM_CAD ( / )
	'()
)

; Ham lay du lieu table tu CAD
(defun CAEX_GET_LISTDATATABLE_FROM_CAD ( / )
	'()
)

; Ham tao table cho Excel
(defun CAEX_CREATE_TABLE_FOR_EXCEL ( / )
	(princ "\nTao table cho Excel...")
)

; Ham lay du lieu table tu Excel
(defun CAEX_GET_LISTDATATABLE_FROM_EXCEL ( NumColTotal NumRowTotal / )
	(list '() '() '())
)

; Ham lay du lieu line tu Excel
(defun CAEX_GET_LISTDATALINE_FROM_EXCEL ( / )
	'()
)

; Ham tao table cho CAD
(defun CAEX_CREATE_TABLE_FOR_CAD ( / )
	'()
)

; Ham pick point cho table
(defun CAEX_PICK_POINT_FOR_TABLE ( / )
	(princ "\nChon diem cho table...")
)

; Ham tim gia tri pho bien
(defun CAEX_FIND_VALUE_POPULAR ( lst / )
	(if lst (car lst) 1.0)
)

; Ham xu ly string content
(defun CAEX_GET_LISTSTRINGCONTENT ( obj / content)
	(setq content "")
	(vl-catch-all-apply
		'(lambda ()
			(setq content (vla-get-TextString obj))
		)
	)
	content
)

; Ham chuyen string thanh list
(defun CAEX_STRING_TO_LIST_NEW ( str delimiter / )
	(if str (list str) '())
)

; Ham tao chuoi ngau nhien
(defun CAEX_RANDOM_STRING ( length / chars result i)
	(setq chars "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	(setq result "")
	(setq i 0)
	(repeat length
		(setq result (strcat result (substr chars (1+ (rem (getvar "MILLISECS") 36)) 1)))
		(setq i (1+ i))
	)
	result
)

; Ham kiem tra file ton tai
(defun CAEX_CHECK_FILE_EXIST ( filename / )
	(if (findfile filename) T nil)
)

; Ham chuyen list thanh string
(defun CAEX_LIST_TO_STRING ( lst separator / result)
	(setq result "")
	(if lst
		(progn
			(setq result (car lst))
			(foreach item (cdr lst)
				(setq result (strcat result separator item))
			)
		)
	)
	result
)

; Ham chuyen string address thanh list address
(defun CAEX_STRINGADDRESS_TO_LISTADDRESS ( str / )
	(list 1 1 1 1)
)

; Ham chuyen list address thanh string address
(defun CAEX_LISTADDRESS_TO_STRINGADDRESS ( lst / )
	"A1"
)

; Ham lay point min max cua text
(defun CAEX_GET_POINTMIN_POINTMAX_TEXT_MTEXT ( obj / )
	(list (list 0 0) (list 1 1))
)

; Bien toan cuc cho CTE/ETC
(if (not SeparatorCsv) (setq SeparatorCsv ","))
(if (not FullNameFileXlsx) (setq FullNameFileXlsx ""))
(if (not CheckActiveCell) (setq CheckActiveCell T))
(if (not VlaSheetActive) (setq VlaSheetActive nil))
(if (not ScaleTable) (setq ScaleTable 1.0))
(if (not ListDataTable) (setq ListDataTable '()))
(if (not ListCoordinateX) (setq ListCoordinateX '()))
(if (not ListCoordinateY) (setq ListCoordinateY '()))
(if (not ListVlaObjectDelete) (setq ListVlaObjectDelete '()))

; Thong bao CTE/ETC da duoc tich hop
(princ "\n=== CTE/ETC FUNCTIONS INTEGRATED ===")
(princ "\nNote: CTE va ETC da duoc tich hop co ban.")
(princ "\nDe su dung day du, vui long tham khao file goc HNP_Cad Link Excel_CTE_fixed.lsp")

(princ)
